"""
Aplicação principal usando NumPy e Gradio.

Esta aplicação demonstra várias funcionalidades do NumPy através de uma interface
web interativa criada com Gradio.
"""

import numpy as np
import gradio as gr
import matplotlib.pyplot as plt
from typing import Tuple, List, Union
import io
import base64
import cmath
from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic


def solve_quadratic_equation(a: float, b: float, c: float) -> Tuple[str, str]:
    """
    Resolve uma equação de segundo grau ax² + bx + c = 0.

    Args:
        a: Coeficiente de x²
        b: Coeficiente de x
        c: Termo independente

    Returns:
        Tuple com resultado detalhado e gráfico da função
    """
    # Usar o módulo separado para resolver a equação
    solution = solve_quadratic(a, b, c)
    result_text = format_solution(solution)
    plot_fig = plot_quadratic(a, b, c, solution)

    return result_text, plot_fig


def generate_random_array(size: int, distribution: str) -> <PERSON><PERSON>[str, str]:
    """
    Gera um array aleatório com a distribuição especificada.
    
    Args:
        size: Tamanho do array
        distribution: Tipo de distribuição ('normal', 'uniform', 'exponential')
    
    Returns:
        Tuple com estatísticas do array e gráfico
    """
    if distribution == "normal":
        arr = np.random.normal(0, 1, size)
    elif distribution == "uniform":
        arr = np.random.uniform(-1, 1, size)
    elif distribution == "exponential":
        arr = np.random.exponential(1, size)
    else:
        arr = np.random.random(size)
    
    # Calcular estatísticas
    stats = f"""
    📊 Estatísticas do Array:
    • Tamanho: {arr.size}
    • Média: {np.mean(arr):.4f}
    • Desvio Padrão: {np.std(arr):.4f}
    • Mínimo: {np.min(arr):.4f}
    • Máximo: {np.max(arr):.4f}
    • Mediana: {np.median(arr):.4f}
    """
    
    # Criar gráfico
    plt.figure(figsize=(10, 6))
    plt.subplot(1, 2, 1)
    plt.hist(arr, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title(f'Histograma - Distribuição {distribution.title()}')
    plt.xlabel('Valor')
    plt.ylabel('Frequência')
    
    plt.subplot(1, 2, 2)
    plt.plot(arr[:min(100, len(arr))], 'o-', markersize=3, linewidth=1)
    plt.title('Primeiros 100 valores')
    plt.xlabel('Índice')
    plt.ylabel('Valor')
    
    plt.tight_layout()
    
    return stats, plt


def matrix_operations(matrix_a: str, matrix_b: str, operation: str) -> str:
    """
    Realiza operações entre matrizes.
    
    Args:
        matrix_a: Matriz A como string
        matrix_b: Matriz B como string
        operation: Tipo de operação
    
    Returns:
        Resultado da operação
    """
    try:
        # Converter strings para arrays NumPy
        a = np.array(eval(matrix_a))
        b = np.array(eval(matrix_b))
        
        if operation == "Adição":
            result = a + b
            op_symbol = "+"
        elif operation == "Subtração":
            result = a - b
            op_symbol = "-"
        elif operation == "Multiplicação":
            result = a * b
            op_symbol = "*"
        elif operation == "Multiplicação de Matrizes":
            result = np.dot(a, b)
            op_symbol = "@"
        elif operation == "Divisão":
            result = a / b
            op_symbol = "/"
        else:
            return "Operação não suportada"
        
        return f"""
        🔢 Operação: {operation}
        
        Matriz A:
        {a}
        
        {op_symbol}
        
        Matriz B:
        {b}
        
        =
        
        Resultado:
        {result}
        
        📏 Dimensões do resultado: {result.shape}
        """
        
    except Exception as e:
        return f"❌ Erro: {str(e)}\n\nVerifique se as matrizes estão no formato correto: [[1,2],[3,4]]"


def fourier_analysis(signal_type: str, frequency: float, noise_level: float) -> Tuple[str, str]:
    """
    Demonstra análise de Fourier com NumPy.
    
    Args:
        signal_type: Tipo de sinal
        frequency: Frequência do sinal
        noise_level: Nível de ruído
    
    Returns:
        Informações do sinal e gráficos
    """
    # Gerar sinal
    t = np.linspace(0, 1, 1000)
    
    if signal_type == "Senoidal":
        signal = np.sin(2 * np.pi * frequency * t)
    elif signal_type == "Cossenoidal":
        signal = np.cos(2 * np.pi * frequency * t)
    elif signal_type == "Quadrada":
        signal = np.sign(np.sin(2 * np.pi * frequency * t))
    else:
        signal = np.sin(2 * np.pi * frequency * t)
    
    # Adicionar ruído
    noise = np.random.normal(0, noise_level, len(signal))
    noisy_signal = signal + noise
    
    # Análise de Fourier
    fft = np.fft.fft(noisy_signal)
    freqs = np.fft.fftfreq(len(t), t[1] - t[0])
    
    # Informações
    info = f"""
    🌊 Análise de Fourier:
    • Tipo de sinal: {signal_type}
    • Frequência: {frequency} Hz
    • Nível de ruído: {noise_level}
    • Pontos do sinal: {len(signal)}
    • Frequência dominante: {freqs[np.argmax(np.abs(fft[:len(fft)//2]))]} Hz
    """
    
    # Gráficos
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(t[:200], signal[:200], 'b-', label='Sinal original')
    plt.title('Sinal Original')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(t[:200], noisy_signal[:200], 'r-', label='Sinal com ruído')
    plt.title('Sinal com Ruído')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(freqs[:len(freqs)//2], np.abs(fft[:len(fft)//2]))
    plt.title('Espectro de Frequência')
    plt.xlabel('Frequência (Hz)')
    plt.ylabel('Magnitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    plt.specgram(noisy_signal, Fs=1/(t[1]-t[0]))
    plt.title('Espectrograma')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Frequência (Hz)')
    
    plt.tight_layout()
    
    return info, plt


def create_interface():
    """Cria a interface Gradio."""
    
    with gr.Blocks(title="NumPy + Gradio Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🔢 NumPy + Gradio Demo

        Esta aplicação demonstra várias funcionalidades do NumPy através de uma interface interativa.
        Explore as diferentes abas para ver exemplos de:
        - Geração de arrays aleatórios
        - Operações com matrizes
        - Resolução de equações quadráticas
        - Análise de Fourier
        """)
        
        with gr.Tabs():
            # Aba 1: Arrays Aleatórios
            with gr.TabItem("🎲 Arrays Aleatórios"):
                gr.Markdown("### Geração e Análise de Arrays Aleatórios")
                
                with gr.Row():
                    with gr.Column():
                        size_input = gr.Slider(
                            minimum=10, maximum=10000, value=1000, step=10,
                            label="Tamanho do Array"
                        )
                        dist_input = gr.Dropdown(
                            choices=["normal", "uniform", "exponential"],
                            value="normal",
                            label="Distribuição"
                        )
                        generate_btn = gr.Button("Gerar Array", variant="primary")
                    
                    with gr.Column():
                        stats_output = gr.Textbox(
                            label="Estatísticas",
                            lines=8,
                            max_lines=10
                        )
                
                plot_output = gr.Plot(label="Visualização")
                
                generate_btn.click(
                    fn=generate_random_array,
                    inputs=[size_input, dist_input],
                    outputs=[stats_output, plot_output]
                )
            
            # Aba 2: Operações com Matrizes
            with gr.TabItem("🔢 Operações com Matrizes"):
                gr.Markdown("### Operações Matemáticas entre Matrizes")
                gr.Markdown("**Formato das matrizes:** `[[1,2],[3,4]]` para matriz 2x2")
                
                with gr.Row():
                    with gr.Column():
                        matrix_a_input = gr.Textbox(
                            label="Matriz A",
                            value="[[1,2],[3,4]]",
                            placeholder="[[1,2],[3,4]]"
                        )
                        matrix_b_input = gr.Textbox(
                            label="Matriz B",
                            value="[[5,6],[7,8]]",
                            placeholder="[[5,6],[7,8]]"
                        )
                        operation_input = gr.Dropdown(
                            choices=["Adição", "Subtração", "Multiplicação", "Multiplicação de Matrizes", "Divisão"],
                            value="Adição",
                            label="Operação"
                        )
                        calc_btn = gr.Button("Calcular", variant="primary")
                    
                    with gr.Column():
                        result_output = gr.Textbox(
                            label="Resultado",
                            lines=15,
                            max_lines=20
                        )
                
                calc_btn.click(
                    fn=matrix_operations,
                    inputs=[matrix_a_input, matrix_b_input, operation_input],
                    outputs=[result_output]
                )
            
            # Aba 3: Equações Quadráticas
            with gr.TabItem("📐 Equações Quadráticas"):
                gr.Markdown("### Resolução de Equações de Segundo Grau")
                gr.Markdown("**Formato:** ax² + bx + c = 0")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("#### Coeficientes da Equação")
                        a_input = gr.Number(
                            label="Coeficiente a (x²)",
                            value=1,
                            info="Se a = 0, será uma equação linear"
                        )
                        b_input = gr.Number(
                            label="Coeficiente b (x)",
                            value=-5,
                            info="Coeficiente do termo linear"
                        )
                        c_input = gr.Number(
                            label="Coeficiente c (termo independente)",
                            value=6,
                            info="Termo constante da equação"
                        )
                        solve_btn = gr.Button("Resolver Equação", variant="primary")

                        # Exemplos pré-definidos
                        gr.Markdown("#### 📚 Exemplos Rápidos")
                        with gr.Row():
                            example1_btn = gr.Button("x² - 5x + 6 = 0", size="sm")
                            example2_btn = gr.Button("x² - 4x + 4 = 0", size="sm")
                        with gr.Row():
                            example3_btn = gr.Button("x² + x + 1 = 0", size="sm")
                            example4_btn = gr.Button("2x - 4 = 0", size="sm")

                    with gr.Column():
                        equation_result = gr.Textbox(
                            label="Solução da Equação",
                            lines=12,
                            max_lines=15
                        )

                equation_plot = gr.Plot(label="Gráfico da Função")

                # Eventos dos botões
                solve_btn.click(
                    fn=solve_quadratic_equation,
                    inputs=[a_input, b_input, c_input],
                    outputs=[equation_result, equation_plot]
                )

                # Exemplos pré-definidos
                example1_btn.click(
                    lambda: (1, -5, 6),
                    outputs=[a_input, b_input, c_input]
                )
                example2_btn.click(
                    lambda: (1, -4, 4),
                    outputs=[a_input, b_input, c_input]
                )
                example3_btn.click(
                    lambda: (1, 1, 1),
                    outputs=[a_input, b_input, c_input]
                )
                example4_btn.click(
                    lambda: (0, 2, -4),
                    outputs=[a_input, b_input, c_input]
                )

            # Aba 4: Análise de Fourier
            with gr.TabItem("🌊 Análise de Fourier"):
                gr.Markdown("### Transformada de Fourier e Análise de Sinais")
                
                with gr.Row():
                    with gr.Column():
                        signal_type_input = gr.Dropdown(
                            choices=["Senoidal", "Cossenoidal", "Quadrada"],
                            value="Senoidal",
                            label="Tipo de Sinal"
                        )
                        frequency_input = gr.Slider(
                            minimum=1, maximum=50, value=5, step=1,
                            label="Frequência (Hz)"
                        )
                        noise_input = gr.Slider(
                            minimum=0, maximum=1, value=0.1, step=0.01,
                            label="Nível de Ruído"
                        )
                        fourier_btn = gr.Button("Analisar", variant="primary")
                    
                    with gr.Column():
                        fourier_info = gr.Textbox(
                            label="Informações do Sinal",
                            lines=8,
                            max_lines=10
                        )
                
                fourier_plot = gr.Plot(label="Análise de Fourier")
                
                fourier_btn.click(
                    fn=fourier_analysis,
                    inputs=[signal_type_input, frequency_input, noise_input],
                    outputs=[fourier_info, fourier_plot]
                )
        
        gr.Markdown("""
        ---
        💡 **Dicas:**
        - Experimente diferentes parâmetros para ver como afetam os resultados
        - Use matrizes pequenas para operações mais rápidas
        - Para equações quadráticas, teste os exemplos pré-definidos
        - A análise de Fourier é útil para processamento de sinais
        """)
    
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando aplicação NumPy + Gradio...")
    
    # Criar e lançar a interface
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )


if __name__ == "__main__":
    main()
